document.forms[0].onsubmit = function(e) {
    e.preventDefault();
    const email = document.getElementById('email').value;
    chrome.storage.local.set({ 'emailAddr': email }, function() {
      if (chrome.runtime.lastError) {
        console.error('Error saving email:', chrome.runtime.lastError);
      } else {
        chrome.runtime.sendMessage({ type: "email_saved", email: email });
        window.close();
      }
    });
  };